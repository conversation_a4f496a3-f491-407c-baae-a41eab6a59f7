#!/usr/bin/env python3
"""
Test script to verify that the training validation fix works correctly.
This script tests the scenario where:
1. User deletes existing person data
2. Collects new face samples
3. Attempts to train the model

The fix ensures that the training validation correctly detects newly collected samples.
"""

import os
import sys
import shutil
import tempfile
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

from face_recognition_engine import FaceRecognitionEngine

def test_training_validation_after_deletion():
    """Test that training validation works correctly after data deletion and fresh collection."""
    
    print("🧪 Testing Training Validation Fix")
    print("=" * 50)
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        dataset_path = os.path.join(temp_dir, "test_dataset")
        model_path = os.path.join(temp_dir, "test_model.pkl")
        
        # Initialize face recognition engine
        engine = FaceRecognitionEngine(dataset_path=dataset_path, model_path=model_path)
        
        print(f"📁 Test dataset path: {dataset_path}")
        print(f"🤖 Test model path: {model_path}")
        print()
        
        # Step 1: Create some initial test data
        print("📝 Step 1: Creating initial test data...")
        person1_dir = os.path.join(dataset_path, "TestPerson1")
        os.makedirs(person1_dir, exist_ok=True)
        
        # Create dummy image files
        for i in range(25):
            dummy_file = os.path.join(person1_dir, f"sample_{i:03d}.jpg")
            with open(dummy_file, 'w') as f:
                f.write("dummy_image_data")
        
        # Check initial dataset info
        initial_info = engine.get_dataset_info()
        print(f"   ✅ Initial people: {initial_info['total_people']}")
        print(f"   ✅ Initial samples: {initial_info['total_samples']}")
        print()
        
        # Step 2: Delete the person data (simulating user deletion)
        print("🗑️ Step 2: Deleting person data...")
        success = engine.delete_person_data("TestPerson1")
        print(f"   {'✅' if success else '❌'} Deletion result: {success}")
        
        # Check dataset info after deletion
        after_deletion_info = engine.get_dataset_info()
        print(f"   ✅ People after deletion: {after_deletion_info['total_people']}")
        print(f"   ✅ Samples after deletion: {after_deletion_info['total_samples']}")
        print()
        
        # Step 3: Add fresh data (simulating new collection)
        print("📸 Step 3: Adding fresh face samples...")
        person2_dir = os.path.join(dataset_path, "TestPerson2")
        os.makedirs(person2_dir, exist_ok=True)
        
        # Create new dummy image files
        for i in range(30):
            dummy_file = os.path.join(person2_dir, f"sample_{i:03d}.jpg")
            with open(dummy_file, 'w') as f:
                f.write("dummy_image_data_new")
        
        # Check dataset info after fresh collection
        after_collection_info = engine.get_dataset_info()
        print(f"   ✅ People after fresh collection: {after_collection_info['total_people']}")
        print(f"   ✅ Samples after fresh collection: {after_collection_info['total_samples']}")
        print()
        
        # Step 4: Test training validation logic
        print("🧠 Step 4: Testing training validation...")
        
        # This simulates the validation logic from the UI
        people_check = "✅" if after_collection_info['total_people'] >= 1 else "❌"
        samples_check = "✅" if after_collection_info['total_samples'] >= 20 else "❌"
        min_person_samples = min([person['samples'] for person in after_collection_info['people']]) if after_collection_info['people'] else 0
        person_samples_check = "✅" if min_person_samples >= 20 else "❌"
        can_train = after_collection_info['total_people'] > 0 and after_collection_info['total_samples'] >= 20
        
        print(f"   {people_check} People: {after_collection_info['total_people']}/1+ required")
        print(f"   {samples_check} Samples: {after_collection_info['total_samples']}/20+ recommended")
        print(f"   {person_samples_check} Min per person: {min_person_samples}/20+ recommended")
        print(f"   {'✅' if can_train else '❌'} Can train model: {can_train}")
        print()
        
        # Step 5: Verify the fix works
        print("🔍 Step 5: Verification Results")
        print("-" * 30)
        
        if can_train:
            print("✅ SUCCESS: Training validation correctly detects fresh data!")
            print("✅ The fix is working - users can now train after deletion and fresh collection.")
        else:
            print("❌ FAILURE: Training validation still shows stale data!")
            print("❌ The fix needs more work.")
        
        print()
        
        # Step 6: Test actual training (optional)
        if can_train:
            print("🚀 Step 6: Testing actual model training...")
            try:
                # Note: This will fail because we're using dummy image files,
                # but it will test the data detection logic
                results = engine.train_model()
                print(f"   Training status: {results['status']}")
                if results['status'] == 'error':
                    print(f"   Expected error (dummy data): {results.get('error', 'Unknown')}")
            except Exception as e:
                print(f"   Expected exception (dummy data): {e}")
        
        print()
        print("🎯 Test Summary:")
        print(f"   • Initial data: {initial_info['total_people']} people, {initial_info['total_samples']} samples")
        print(f"   • After deletion: {after_deletion_info['total_people']} people, {after_deletion_info['total_samples']} samples")
        print(f"   • After fresh collection: {after_collection_info['total_people']} people, {after_collection_info['total_samples']} samples")
        print(f"   • Training validation: {'PASS' if can_train else 'FAIL'}")

if __name__ == "__main__":
    test_training_validation_after_deletion()
